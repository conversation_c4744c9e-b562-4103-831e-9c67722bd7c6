<?php
require_once 'config/db.php';
require_once 'includes/functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>ModernShop</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="css/main.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Meta tags for SEO and social sharing -->
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Modern e-commerce store with the latest products at great prices'; ?>">
    <meta name="keywords" content="ecommerce, shopping, online store, products">
    <meta name="author" content="ModernShop">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title : 'ModernShop'; ?>">
    <meta property="og:description" content="<?php echo isset($page_description) ? $page_description : 'Modern e-commerce store with the latest products at great prices'; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    
    <!-- Additional CSS for specific pages -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link rel="stylesheet" href="<?php echo $css_file; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <div class="site-wrapper">
        <header class="site-header">
            <div class="container">
                <nav class="header-nav">
                    <!-- Logo -->
                    <a href="index.php" class="logo">
                        <i class="fas fa-store"></i>
                        ModernShop
                    </a>
                    
                    <!-- Main Navigation -->
                    <div class="nav-menu">
                        <a href="index.php" class="nav-link <?php echo is_active_page('index'); ?>">
                            <i class="fas fa-home"></i>
                            Home
                        </a>
                        
                        <?php if (is_logged_in()): ?>
                            <?php if (is_admin()): ?>
                                <a href="admin/index.php" class="nav-link">
                                    <i class="fas fa-tachometer-alt"></i>
                                    Admin
                                </a>
                            <?php endif; ?>
                            
                            <a href="orders.php" class="nav-link <?php echo is_active_page('orders'); ?>">
                                <i class="fas fa-box"></i>
                                My Orders
                            </a>
                        <?php endif; ?>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="user-menu">
                        <!-- Shopping Cart -->
                        <?php if (!is_admin()): ?>
                            <a href="cart.php" class="cart-icon">
                                <i class="fas fa-shopping-cart"></i>
                                <?php 
                                $cart_count = get_cart_count();
                                if ($cart_count > 0): 
                                ?>
                                    <span class="cart-count"><?php echo $cart_count; ?></span>
                                <?php endif; ?>
                            </a>
                        <?php endif; ?>
                        
                        <!-- User Authentication -->
                        <?php if (is_logged_in()): ?>
                            <div class="user-dropdown">
                                <span class="nav-link">
                                    <i class="fas fa-user"></i>
                                    <?php echo htmlspecialchars($_SESSION['username']); ?>
                                </span>
                                <a href="logout.php" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-sign-out-alt"></i>
                                    Logout
                                </a>
                            </div>
                        <?php else: ?>
                            <a href="login.php" class="btn btn-outline btn-sm">
                                <i class="fas fa-sign-in-alt"></i>
                                Login
                            </a>
                            <a href="register.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-user-plus"></i>
                                Register
                            </a>
                        <?php endif; ?>
                    </div>
                </nav>
            </div>
        </header>
        
        <!-- Flash Messages -->
        <div class="container">
            <?php display_flash_message(); ?>
        </div>
        
        <main class="main-content">
