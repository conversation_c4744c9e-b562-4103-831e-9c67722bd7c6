<?php
// REST API for Products
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';
require_once '../includes/security.php';

// Initialize security
$rateLimiter = initializeSecurity($pdo);

// Rate limiting for API
$clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
if (!$rateLimiter->checkRateLimit($clientIP, 'api_request', 60, 60)) {
    http_response_code(429);
    echo json_encode(['error' => 'Rate limit exceeded']);
    exit;
}

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

// Extract product ID if present
$productId = null;
if (isset($pathParts[2]) && is_numeric($pathParts[2])) {
    $productId = (int)$pathParts[2];
}

// Handle different HTTP methods
try {
    switch ($method) {
        case 'GET':
            handleGetRequest($pdo, $productId);
            break;
            
        case 'POST':
            handlePostRequest($pdo);
            break;
            
        case 'PUT':
            handlePutRequest($pdo, $productId);
            break;
            
        case 'DELETE':
            handleDeleteRequest($pdo, $productId);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
    error_log("API Error: " . $e->getMessage());
}

// GET /api/products or /api/products/{id}
function handleGetRequest($pdo, $productId) {
    if ($productId) {
        // Get single product
        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.id = ?
        ");
        $stmt->execute([$productId]);
        $product = $stmt->fetch();
        
        if (!$product) {
            http_response_code(404);
            echo json_encode(['error' => 'Product not found']);
            return;
        }
        
        // Format product data
        $product['price'] = (float)$product['price'];
        $product['stock'] = (int)$product['stock'];
        $product['image_url'] = get_product_image($product['image']);
        
        echo json_encode(['data' => $product]);
    } else {
        // Get multiple products with pagination and filters
        $page = max(1, (int)($_GET['page'] ?? 1));
        $limit = min(50, max(1, (int)($_GET['limit'] ?? 12)));
        $offset = ($page - 1) * $limit;
        
        $search = $_GET['search'] ?? '';
        $category = (int)($_GET['category'] ?? 0);
        $minPrice = (float)($_GET['min_price'] ?? 0);
        $maxPrice = (float)($_GET['max_price'] ?? 0);
        $sortBy = $_GET['sort'] ?? 'id';
        $sortOrder = strtoupper($_GET['order'] ?? 'DESC');
        
        // Validate sort parameters
        $allowedSortFields = ['id', 'name', 'price', 'stock', 'created_at'];
        $allowedSortOrders = ['ASC', 'DESC'];
        
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'id';
        }
        
        if (!in_array($sortOrder, $allowedSortOrders)) {
            $sortOrder = 'DESC';
        }
        
        // Build query
        $whereConditions = [];
        $params = [];
        
        if (!empty($search)) {
            $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
            $searchTerm = "%{$search}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if ($category > 0) {
            $whereConditions[] = "p.category_id = ?";
            $params[] = $category;
        }
        
        if ($minPrice > 0) {
            $whereConditions[] = "p.price >= ?";
            $params[] = $minPrice;
        }
        
        if ($maxPrice > 0) {
            $whereConditions[] = "p.price <= ?";
            $params[] = $maxPrice;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Get total count
        $countQuery = "SELECT COUNT(*) as total FROM products p {$whereClause}";
        $stmt = $pdo->prepare($countQuery);
        $stmt->execute($params);
        $total = $stmt->fetch()['total'];
        
        // Get products
        $query = "
            SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            {$whereClause} 
            ORDER BY p.{$sortBy} {$sortOrder} 
            LIMIT {$limit} OFFSET {$offset}
        ";
        
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $products = $stmt->fetchAll();
        
        // Format product data
        foreach ($products as &$product) {
            $product['price'] = (float)$product['price'];
            $product['stock'] = (int)$product['stock'];
            $product['image_url'] = get_product_image($product['image']);
        }
        
        $response = [
            'data' => $products,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => (int)$total,
                'total_pages' => ceil($total / $limit)
            ],
            'filters' => [
                'search' => $search,
                'category' => $category,
                'min_price' => $minPrice,
                'max_price' => $maxPrice,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder
            ]
        ];
        
        echo json_encode($response);
    }
}

// POST /api/products
function handlePostRequest($pdo) {
    // Require admin authentication
    if (!is_admin()) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    // Validate required fields
    $required = ['name', 'description', 'price', 'stock'];
    foreach ($required as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '{$field}' is required"]);
            return;
        }
    }
    
    // Validate data types
    if (!is_numeric($input['price']) || $input['price'] < 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Price must be a positive number']);
        return;
    }
    
    if (!is_numeric($input['stock']) || $input['stock'] < 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Stock must be a non-negative integer']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO products (name, description, price, stock, category_id, image) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $input['name'],
            $input['description'],
            (float)$input['price'],
            (int)$input['stock'],
            (int)($input['category_id'] ?? 0),
            $input['image'] ?? null
        ]);
        
        $productId = $pdo->lastInsertId();
        
        // Return created product
        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.id = ?
        ");
        $stmt->execute([$productId]);
        $product = $stmt->fetch();
        
        $product['price'] = (float)$product['price'];
        $product['stock'] = (int)$product['stock'];
        $product['image_url'] = get_product_image($product['image']);
        
        http_response_code(201);
        echo json_encode(['data' => $product]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create product']);
    }
}

// PUT /api/products/{id}
function handlePutRequest($pdo, $productId) {
    if (!$productId) {
        http_response_code(400);
        echo json_encode(['error' => 'Product ID is required']);
        return;
    }
    
    // Require admin authentication
    if (!is_admin()) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    // Check if product exists
    $stmt = $pdo->prepare("SELECT id FROM products WHERE id = ?");
    $stmt->execute([$productId]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['error' => 'Product not found']);
        return;
    }
    
    // Build update query dynamically
    $updateFields = [];
    $params = [];
    
    $allowedFields = ['name', 'description', 'price', 'stock', 'category_id', 'image'];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            $updateFields[] = "{$field} = ?";
            
            if ($field === 'price') {
                $params[] = (float)$input[$field];
            } elseif ($field === 'stock' || $field === 'category_id') {
                $params[] = (int)$input[$field];
            } else {
                $params[] = $input[$field];
            }
        }
    }
    
    if (empty($updateFields)) {
        http_response_code(400);
        echo json_encode(['error' => 'No valid fields to update']);
        return;
    }
    
    $params[] = $productId;
    
    try {
        $query = "UPDATE products SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        
        // Return updated product
        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.id = ?
        ");
        $stmt->execute([$productId]);
        $product = $stmt->fetch();
        
        $product['price'] = (float)$product['price'];
        $product['stock'] = (int)$product['stock'];
        $product['image_url'] = get_product_image($product['image']);
        
        echo json_encode(['data' => $product]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update product']);
    }
}

// DELETE /api/products/{id}
function handleDeleteRequest($pdo, $productId) {
    if (!$productId) {
        http_response_code(400);
        echo json_encode(['error' => 'Product ID is required']);
        return;
    }
    
    // Require admin authentication
    if (!is_admin()) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized']);
        return;
    }
    
    try {
        // Check if product exists
        $stmt = $pdo->prepare("SELECT id FROM products WHERE id = ?");
        $stmt->execute([$productId]);
        if (!$stmt->fetch()) {
            http_response_code(404);
            echo json_encode(['error' => 'Product not found']);
            return;
        }
        
        // Delete product
        $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
        $stmt->execute([$productId]);
        
        http_response_code(204);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete product']);
    }
}
?>
