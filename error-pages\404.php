<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - ModernShop</title>
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-primary-100) 100%);
            padding: var(--space-8);
        }
        
        .error-container {
            text-align: center;
            max-width: 600px;
            background: var(--color-white);
            padding: var(--space-12);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-2xl);
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: var(--font-weight-bold);
            color: var(--color-primary);
            line-height: 1;
            margin-bottom: var(--space-4);
            background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .error-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-gray-900);
            margin-bottom: var(--space-4);
        }
        
        .error-message {
            font-size: var(--font-size-lg);
            color: var(--color-gray-600);
            margin-bottom: var(--space-8);
            line-height: var(--line-height-relaxed);
        }
        
        .error-actions {
            display: flex;
            gap: var(--space-4);
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-element:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .floating-element:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .floating-element:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        .floating-element:nth-child(4) {
            bottom: 10%;
            right: 20%;
            animation-delay: 1s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: var(--font-size-2xl);
            }
            
            .error-message {
                font-size: var(--font-size-base);
            }
            
            .error-container {
                padding: var(--space-8);
            }
            
            .error-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="error-page">
        <div class="floating-elements">
            <div class="floating-element">
                <i class="fas fa-shopping-bag" style="font-size: 3rem; color: var(--color-primary);"></i>
            </div>
            <div class="floating-element">
                <i class="fas fa-store" style="font-size: 2.5rem; color: var(--color-primary);"></i>
            </div>
            <div class="floating-element">
                <i class="fas fa-gift" style="font-size: 2rem; color: var(--color-primary);"></i>
            </div>
            <div class="floating-element">
                <i class="fas fa-heart" style="font-size: 1.5rem; color: var(--color-primary);"></i>
            </div>
        </div>
        
        <div class="error-container animate-fade-in-up">
            <div class="error-code animate-bounce-in">404</div>
            
            <h1 class="error-title animate-fade-in" style="animation-delay: 0.2s;">
                Oops! Page Not Found
            </h1>
            
            <p class="error-message animate-fade-in" style="animation-delay: 0.4s;">
                The page you're looking for seems to have wandered off into the digital wilderness. 
                Don't worry though, our best products are still waiting for you!
            </p>
            
            <div class="error-actions animate-fade-in" style="animation-delay: 0.6s;">
                <a href="/" class="btn btn-primary btn-lg hover-lift">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
                
                <a href="/search.php" class="btn btn-secondary btn-lg hover-lift">
                    <i class="fas fa-search"></i>
                    Search Products
                </a>
                
                <button onclick="history.back()" class="btn btn-outline btn-lg hover-lift">
                    <i class="fas fa-arrow-left"></i>
                    Go Back
                </button>
            </div>
            
            <!-- Popular Categories -->
            <div style="margin-top: var(--space-12); padding-top: var(--space-8); border-top: 1px solid var(--color-gray-200);">
                <h3 style="color: var(--color-gray-700); margin-bottom: var(--space-6);">
                    Or explore our popular categories:
                </h3>
                
                <div style="display: flex; gap: var(--space-4); justify-content: center; flex-wrap: wrap;">
                    <a href="/search.php?category=1" class="btn btn-outline btn-sm hover-scale">
                        <i class="fas fa-laptop"></i>
                        Electronics
                    </a>
                    <a href="/search.php?category=2" class="btn btn-outline btn-sm hover-scale">
                        <i class="fas fa-tshirt"></i>
                        Clothing
                    </a>
                    <a href="/search.php?category=3" class="btn btn-outline btn-sm hover-scale">
                        <i class="fas fa-home"></i>
                        Home & Garden
                    </a>
                    <a href="/search.php?category=4" class="btn btn-outline btn-sm hover-scale">
                        <i class="fas fa-gamepad"></i>
                        Sports & Games
                    </a>
                </div>
            </div>
            
            <!-- Contact Info -->
            <div style="margin-top: var(--space-8); color: var(--color-gray-600); font-size: var(--font-size-sm);">
                <p>Still can't find what you're looking for?</p>
                <p>
                    <a href="mailto:<EMAIL>" class="text-primary hover:text-primary-dark">
                        Contact our support team
                    </a>
                    or call us at 
                    <a href="tel:+15551234567" class="text-primary hover:text-primary-dark">
                        (*************
                    </a>
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Parallax effect for floating elements
            document.addEventListener('mousemove', function(e) {
                const elements = document.querySelectorAll('.floating-element');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;
                
                elements.forEach((element, index) => {
                    const speed = (index + 1) * 0.5;
                    const xPos = (x - 0.5) * speed * 20;
                    const yPos = (y - 0.5) * speed * 20;
                    
                    element.style.transform = `translate(${xPos}px, ${yPos}px)`;
                });
            });
            
            // Add click effect to error code
            const errorCode = document.querySelector('.error-code');
            errorCode.addEventListener('click', function() {
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = 'bounce 0.6s ease-out';
                }, 10);
            });
            
            // Auto-focus on first action button for accessibility
            const firstButton = document.querySelector('.error-actions .btn');
            if (firstButton) {
                firstButton.focus();
            }
        });
        
        // Add some fun easter eggs
        let clickCount = 0;
        document.querySelector('.error-code').addEventListener('click', function() {
            clickCount++;
            if (clickCount === 5) {
                this.style.animation = 'spin 2s linear infinite';
                setTimeout(() => {
                    this.style.animation = '';
                    clickCount = 0;
                }, 2000);
            }
        });
    </script>
</body>
</html>
