<?php
// Quick extension checker
header('Content-Type: text/plain');

echo "=== PHP Extension Check ===\n\n";

echo "PHP Version: " . PHP_VERSION . "\n";
echo "PHP SAPI: " . PHP_SAPI . "\n\n";

$required = ['pdo', 'pdo_sqlite'];
$optional = ['gd', 'openssl', 'json', 'mbstring'];

echo "REQUIRED EXTENSIONS:\n";
foreach ($required as $ext) {
    $status = extension_loaded($ext) ? '✓ YES' : '✗ NO';
    echo "  $ext: $status\n";
}

echo "\nOPTIONAL EXTENSIONS:\n";
foreach ($optional as $ext) {
    $status = extension_loaded($ext) ? '✓ YES' : '✗ NO';
    echo "  $ext: $status\n";
}

echo "\nALL LOADED EXTENSIONS:\n";
$extensions = get_loaded_extensions();
sort($extensions);
foreach ($extensions as $ext) {
    echo "  - $ext\n";
}

echo "\n=== RECOMMENDATIONS ===\n";

$missing_required = array_filter($required, function($ext) {
    return !extension_loaded($ext);
});

$missing_optional = array_filter($optional, function($ext) {
    return !extension_loaded($ext);
});

if (empty($missing_required)) {
    echo "✓ All required extensions are available!\n";
    echo "✓ You can run ModernShop with SQLite!\n\n";
    
    if (!empty($missing_optional)) {
        echo "⚠ Missing optional extensions: " . implode(', ', $missing_optional) . "\n";
        echo "  Impact:\n";
        if (in_array('gd', $missing_optional)) {
            echo "  - Image processing will be limited\n";
        }
        if (in_array('openssl', $missing_optional)) {
            echo "  - Some security features may not work\n";
        }
        echo "\n";
    } else {
        echo "🎉 All extensions available! Full functionality enabled!\n\n";
    }
    
    echo "NEXT STEPS:\n";
    echo "1. Visit: http://localhost:8000/setup-sqlite.php\n";
    echo "2. Create your admin account\n";
    echo "3. Start using ModernShop!\n";
    
} else {
    echo "❌ Missing required extensions: " . implode(', ', $missing_required) . "\n\n";
    echo "SOLUTIONS:\n";
    echo "1. Use XAMPP (easiest): https://www.apachefriends.org/\n";
    echo "2. Install extensions manually (see INSTALL-EXTENSIONS.md)\n";
    echo "3. Use Docker: docker-compose up\n";
}
?>
