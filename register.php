<?php
$page_title = "Register";
$page_description = "Create your ModernShop account";
$additional_css = ['css/forms.css', 'css/animations.css'];
$additional_js = ['js/forms.js'];

require_once 'config/db.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Redirect if already logged in
if (is_logged_in()) {
    redirect('index.php');
}

$errors = [];
$form_data = [
    'username' => '',
    'email' => '',
    'full_name' => ''
];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $form_data['username'] = sanitize_input($_POST['username'] ?? '');
    $form_data['email'] = sanitize_input($_POST['email'] ?? '');
    $form_data['full_name'] = sanitize_input($_POST['full_name'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    $terms_accepted = isset($_POST['terms']);
    
    // Validate CSRF token
    if (!verify_csrf_token($csrf_token)) {
        $errors['general'] = 'Invalid request. Please try again.';
    } else {
        // Validate input
        $validation_errors = validate_registration(
            $form_data['username'], 
            $form_data['email'], 
            $password, 
            $confirm_password, 
            $form_data['full_name']
        );
        
        // Check terms acceptance
        if (!$terms_accepted) {
            $validation_errors['terms'] = 'You must accept the terms and conditions';
        }
        
        if (empty($validation_errors)) {
            // Attempt registration
            $result = register_user($pdo, $form_data['username'], $form_data['email'], $password, $form_data['full_name']);
            
            if ($result['success']) {
                set_flash_message('Registration successful! Please log in to continue.', 'success');
                redirect('login.php');
            } else {
                $errors['general'] = $result['message'];
            }
        } else {
            $errors = array_merge($errors, $validation_errors);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - ModernShop</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/main.css">
    <?php foreach ($additional_css as $css_file): ?>
        <link rel="stylesheet" href="<?php echo $css_file; ?>">
    <?php endforeach; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="auth-page">
        <div class="auth-container animate-fade-in-up">
            <!-- Header -->
            <div class="auth-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-store"></i>
                    ModernShop
                </a>
                <h1>Create Account</h1>
                <p>Join ModernShop and start your shopping journey</p>
            </div>
            
            <!-- Registration Form -->
            <div class="form-container">
                <?php if (!empty($errors['general'])): ?>
                    <div class="alert alert-error animate-shake">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($errors['general']); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" data-validate class="animate-fade-in" style="animation-delay: 0.2s;">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    
                    <!-- Full Name Field -->
                    <div class="form-group <?php echo isset($errors['full_name']) ? 'error' : ''; ?>">
                        <input 
                            type="text" 
                            name="full_name" 
                            class="form-input" 
                            value="<?php echo htmlspecialchars($form_data['full_name']); ?>"
                            required
                            autocomplete="name"
                        >
                        <label class="form-label">Full Name</label>
                        <?php if (isset($errors['full_name'])): ?>
                            <div class="form-message error">
                                <?php echo htmlspecialchars($errors['full_name']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Username Field -->
                    <div class="form-group <?php echo isset($errors['username']) ? 'error' : ''; ?>">
                        <input 
                            type="text" 
                            name="username" 
                            class="form-input" 
                            value="<?php echo htmlspecialchars($form_data['username']); ?>"
                            required
                            autocomplete="username"
                        >
                        <label class="form-label">Username</label>
                        <?php if (isset($errors['username'])): ?>
                            <div class="form-message error">
                                <?php echo htmlspecialchars($errors['username']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Email Field -->
                    <div class="form-group <?php echo isset($errors['email']) ? 'error' : ''; ?>">
                        <input 
                            type="email" 
                            name="email" 
                            class="form-input" 
                            value="<?php echo htmlspecialchars($form_data['email']); ?>"
                            required
                            autocomplete="email"
                        >
                        <label class="form-label">Email Address</label>
                        <?php if (isset($errors['email'])): ?>
                            <div class="form-message error">
                                <?php echo htmlspecialchars($errors['email']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Password Field -->
                    <div class="form-group <?php echo isset($errors['password']) ? 'error' : ''; ?>">
                        <input 
                            type="password" 
                            name="password" 
                            class="form-input" 
                            required
                            autocomplete="new-password"
                            data-strength
                        >
                        <label class="form-label">Password</label>
                        <?php if (isset($errors['password'])): ?>
                            <div class="form-message error">
                                <?php echo htmlspecialchars($errors['password']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Confirm Password Field -->
                    <div class="form-group <?php echo isset($errors['confirm_password']) ? 'error' : ''; ?>">
                        <input 
                            type="password" 
                            name="confirm_password" 
                            class="form-input" 
                            required
                            autocomplete="new-password"
                        >
                        <label class="form-label">Confirm Password</label>
                        <?php if (isset($errors['confirm_password'])): ?>
                            <div class="form-message error">
                                <?php echo htmlspecialchars($errors['confirm_password']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Terms and Conditions -->
                    <div class="form-checkbox <?php echo isset($errors['terms']) ? 'error' : ''; ?>">
                        <input type="checkbox" id="terms" name="terms" required>
                        <label for="terms">
                            I agree to the <a href="#" class="text-primary">Terms and Conditions</a> 
                            and <a href="#" class="text-primary">Privacy Policy</a>
                        </label>
                        <?php if (isset($errors['terms'])): ?>
                            <div class="form-message error" style="margin-top: var(--space-2);">
                                <?php echo htmlspecialchars($errors['terms']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Newsletter Subscription -->
                    <div class="form-checkbox">
                        <input type="checkbox" id="newsletter" name="newsletter" checked>
                        <label for="newsletter">
                            Subscribe to our newsletter for updates and special offers
                        </label>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-animate">
                            <i class="fas fa-user-plus"></i>
                            Create Account
                        </button>
                    </div>
                </form>
                
                <!-- Divider -->
                <div class="form-divider">
                    <span>or</span>
                </div>
                
                <!-- Login Link -->
                <div class="form-link">
                    <p>Already have an account? 
                        <a href="login.php" class="hover-glow">Sign in here</a>
                    </p>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="auth-footer">
                <p>&copy; <?php echo date('Y'); ?> ModernShop. All rights reserved.</p>
                <p>
                    <a href="index.php">Back to Store</a> | 
                    <a href="#">Privacy Policy</a> | 
                    <a href="#">Terms of Service</a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <?php foreach ($additional_js as $js_file): ?>
        <script src="<?php echo $js_file; ?>"></script>
    <?php endforeach; ?>
    
    <script>
        // Add interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Stagger animation for form fields
            const formGroups = document.querySelectorAll('.form-group');
            formGroups.forEach((group, index) => {
                group.style.opacity = '0';
                group.style.transform = 'translateY(20px)';
                group.style.animation = `fadeInUp 0.6s ease-out forwards`;
                group.style.animationDelay = `${0.3 + (index * 0.1)}s`;
            });
            
            // Add focus effects
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                    this.parentElement.style.transition = 'transform 0.2s ease';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });
            
            // Enhanced submit button interaction
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                    this.style.boxShadow = 'var(--shadow-xl)';
                });
                
                submitBtn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '';
                });
            }
            
            // Add success animation for completed fields
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() && !this.closest('.form-group').classList.contains('error')) {
                        this.closest('.form-group').classList.add('success');
                        
                        // Add checkmark animation
                        setTimeout(() => {
                            const checkmark = document.createElement('i');
                            checkmark.className = 'fas fa-check';
                            checkmark.style.cssText = `
                                position: absolute;
                                right: 12px;
                                top: 50%;
                                transform: translateY(-50%);
                                color: var(--color-success);
                                opacity: 0;
                                animation: fadeIn 0.3s ease-out forwards;
                            `;
                            
                            const existingCheck = this.parentElement.querySelector('.fa-check');
                            if (!existingCheck) {
                                this.parentElement.style.position = 'relative';
                                this.parentElement.appendChild(checkmark);
                            }
                        }, 100);
                    }
                });
            });
        });
    </script>
</body>
</html>
