<?php
/**
 * ModernShop Setup Script
 * Run this script to set up the database and create an admin user
 */

// Check if setup has already been run
if (file_exists('setup_complete.txt')) {
    die('Setup has already been completed. Delete setup_complete.txt to run again.');
}

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    die('PHP 7.4 or higher is required. Current version: ' . PHP_VERSION);
}

// Check required extensions
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'openssl', 'json'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $missing_extensions[] = $ext;
    }
}

if (!empty($missing_extensions)) {
    die('Missing required PHP extensions: ' . implode(', ', $missing_extensions));
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ModernShop Setup</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; }
        .container { max-width: 600px; margin: 50px auto; padding: 20px; }
        .card { background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 40px; }
        h1 { color: #1e293b; margin-bottom: 10px; font-size: 2rem; }
        .subtitle { color: #64748b; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 600; color: #374151; }
        input, select { width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; }
        input:focus, select:focus { outline: none; border-color: #3b82f6; }
        .btn { background: #3b82f6; color: white; padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: 600; }
        .btn:hover { background: #2563eb; }
        .success { background: #10b981; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .error { background: #ef4444; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .step { margin-bottom: 30px; padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #3b82f6; }
        .step h3 { color: #1e293b; margin-bottom: 10px; }
        .requirements { background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 15px; margin-bottom: 20px; }
        .requirements h4 { color: #92400e; margin-bottom: 10px; }
        .requirements ul { margin-left: 20px; }
        .requirements li { color: #92400e; margin-bottom: 5px; }
        .check { color: #10b981; }
        .cross { color: #ef4444; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🛍️ ModernShop Setup</h1>
            <p class="subtitle">Welcome! Let's set up your e-commerce platform.</p>

            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $host = $_POST['host'] ?? 'localhost';
                $dbname = $_POST['dbname'] ?? 'modernshop';
                $username = $_POST['username'] ?? 'root';
                $password = $_POST['password'] ?? '';
                
                $admin_name = $_POST['admin_name'] ?? '';
                $admin_username = $_POST['admin_username'] ?? '';
                $admin_email = $_POST['admin_email'] ?? '';
                $admin_password = $_POST['admin_password'] ?? '';

                try {
                    // Test database connection
                    $pdo = new PDO("mysql:host=$host", $username, $password);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                    // Create database if it doesn't exist
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    $pdo->exec("USE `$dbname`");

                    echo '<div class="success">✅ Database connection successful!</div>';

                    // Read and execute schema
                    if (file_exists('database/schema.sql')) {
                        $schema = file_get_contents('database/schema.sql');
                        $statements = explode(';', $schema);
                        
                        foreach ($statements as $statement) {
                            $statement = trim($statement);
                            if (!empty($statement)) {
                                $pdo->exec($statement);
                            }
                        }
                        echo '<div class="success">✅ Main database schema created!</div>';
                    }

                    // Execute advanced schema if exists
                    if (file_exists('database/advanced_schema.sql')) {
                        $schema = file_get_contents('database/advanced_schema.sql');
                        $statements = explode(';', $schema);
                        
                        foreach ($statements as $statement) {
                            $statement = trim($statement);
                            if (!empty($statement)) {
                                try {
                                    $pdo->exec($statement);
                                } catch (Exception $e) {
                                    // Continue if statement fails (might be duplicate)
                                }
                            }
                        }
                        echo '<div class="success">✅ Advanced features schema created!</div>';
                    }

                    // Create admin user
                    if (!empty($admin_name) && !empty($admin_username) && !empty($admin_email) && !empty($admin_password)) {
                        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                        
                        $stmt = $pdo->prepare("INSERT INTO users (full_name, username, email, password, role) VALUES (?, ?, ?, ?, 'admin')");
                        $stmt->execute([$admin_name, $admin_username, $admin_email, $hashed_password]);
                        
                        echo '<div class="success">✅ Admin user created successfully!</div>';
                    }

                    // Update database configuration
                    $config_content = "<?php
// Database Configuration
\$host = '$host';
\$dbname = '$dbname';
\$username = '$username';
\$password = '$password';

try {
    \$pdo = new PDO(\"mysql:host=\$host;dbname=\$dbname;charset=utf8mb4\", \$username, \$password);
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException \$e) {
    die('Database connection failed: ' . \$e->getMessage());
}
?>";

                    file_put_contents('config/db.php', $config_content);
                    echo '<div class="success">✅ Database configuration updated!</div>';

                    // Create necessary directories
                    $directories = ['uploads', 'uploads/products', 'uploads/users', 'cache', 'logs'];
                    foreach ($directories as $dir) {
                        if (!is_dir($dir)) {
                            mkdir($dir, 0755, true);
                        }
                    }
                    echo '<div class="success">✅ Required directories created!</div>';

                    // Mark setup as complete
                    file_put_contents('setup_complete.txt', date('Y-m-d H:i:s'));

                    echo '<div class="success">
                        <h3>🎉 Setup Complete!</h3>
                        <p>Your ModernShop installation is ready!</p>
                        <br>
                        <strong>Next Steps:</strong><br>
                        1. <a href="index.php" style="color: white; text-decoration: underline;">Visit your store</a><br>
                        2. <a href="admin/" style="color: white; text-decoration: underline;">Access admin panel</a><br>
                        3. Delete this setup.php file for security
                    </div>';

                } catch (Exception $e) {
                    echo '<div class="error">❌ Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            } else {
            ?>

            <div class="requirements">
                <h4>📋 System Requirements Check</h4>
                <ul>
                    <li><span class="check">✅</span> PHP <?php echo PHP_VERSION; ?> (7.4+ required)</li>
                    <?php foreach ($required_extensions as $ext): ?>
                        <li><span class="check">✅</span> <?php echo $ext; ?> extension</li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <form method="POST">
                <div class="step">
                    <h3>1. Database Configuration</h3>
                    <div class="form-group">
                        <label>Database Host:</label>
                        <input type="text" name="host" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label>Database Name:</label>
                        <input type="text" name="dbname" value="modernshop" required>
                    </div>
                    <div class="form-group">
                        <label>Database Username:</label>
                        <input type="text" name="username" value="root" required>
                    </div>
                    <div class="form-group">
                        <label>Database Password:</label>
                        <input type="password" name="password" placeholder="Leave empty if no password">
                    </div>
                </div>

                <div class="step">
                    <h3>2. Admin User Setup</h3>
                    <div class="form-group">
                        <label>Full Name:</label>
                        <input type="text" name="admin_name" placeholder="John Doe" required>
                    </div>
                    <div class="form-group">
                        <label>Username:</label>
                        <input type="text" name="admin_username" placeholder="admin" required>
                    </div>
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" name="admin_email" placeholder="<EMAIL>" required>
                    </div>
                    <div class="form-group">
                        <label>Password:</label>
                        <input type="password" name="admin_password" placeholder="Strong password" required>
                    </div>
                </div>

                <button type="submit" class="btn">🚀 Install ModernShop</button>
            </form>

            <?php } ?>
        </div>
    </div>
</body>
</html>
