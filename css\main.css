/* Main Stylesheet - Combines all CSS modules */

/* Import all CSS modules in correct order */
@import url('./reset.css');
@import url('./variables.css');
@import url('./typography.css');
@import url('./layout.css');
@import url('./components.css');

/* Global Styles */
html {
    scroll-behavior: smooth;
}

body {
    background-color: var(--color-gray-50);
    min-height: 100vh;
}

/* Main Layout Structure */
.site-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
    padding-top: var(--space-8);
    padding-bottom: var(--space-8);
}

/* Header Styles */
.site-header {
    background-color: var(--color-white);
    border-bottom: 1px solid var(--color-gray-200);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.header-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) 0;
}

.logo {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    text-decoration: none;
    font-family: var(--font-family-heading);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.nav-link {
    color: var(--color-gray-700);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--color-primary);
}

.nav-link.active {
    color: var(--color-primary);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--color-primary);
    border-radius: var(--radius-full);
}

/* User Menu */
.user-menu {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.cart-icon {
    position: relative;
    color: var(--color-gray-700);
    font-size: var(--font-size-xl);
    transition: color var(--transition-fast);
}

.cart-icon:hover {
    color: var(--color-primary);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--color-error);
    color: var(--color-white);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    text-align: center;
}

/* Footer Styles */
.site-footer {
    background-color: var(--color-gray-900);
    color: var(--color-gray-300);
    padding: var(--space-12) 0 var(--space-6);
    margin-top: auto;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}

.footer-section h3 {
    color: var(--color-white);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-4);
}

.footer-section p,
.footer-section a {
    color: var(--color-gray-400);
    line-height: var(--line-height-relaxed);
}

.footer-section a:hover {
    color: var(--color-white);
}

.footer-bottom {
    border-top: 1px solid var(--color-gray-800);
    padding-top: var(--space-6);
    text-align: center;
    color: var(--color-gray-500);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: var(--color-white);
    padding: var(--space-20) 0;
    text-align: center;
}

.hero h1 {
    color: var(--color-white);
    margin-bottom: var(--space-6);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.hero p {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-8);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Product Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-8);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--space-12);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-900);
    margin-bottom: var(--space-4);
}

.section-subtitle {
    font-size: var(--font-size-xl);
    color: var(--color-gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-nav {
        flex-direction: column;
        gap: var(--space-4);
        padding: var(--space-3) 0;
    }
    
    .nav-menu {
        gap: var(--space-4);
    }
    
    .hero {
        padding: var(--space-12) 0;
    }
    
    .hero h1 {
        font-size: var(--font-size-3xl);
    }
    
    .hero p {
        font-size: var(--font-size-lg);
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--space-4);
    }
    
    .main-content {
        padding-top: var(--space-6);
        padding-bottom: var(--space-6);
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding-left: var(--space-3);
        padding-right: var(--space-3);
    }
}
