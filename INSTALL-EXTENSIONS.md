# 🔧 Installing PHP Extensions for ModernShop

This guide will help you install the required PHP extensions: `gd` and `openssl`.

## 🎯 Quick Solutions (Recommended)

### Option 1: XAMPP (Easiest - Windows/Mac/Linux)
**✅ All extensions included, zero configuration**

1. **Download XAMPP**: https://www.apachefriends.org/download.html
2. **Install** with default settings
3. **Start Apache** in XAMPP Control Panel
4. **Copy project** to `C:\xampp\htdocs\modernshop` (Windows) or `/Applications/XAMPP/htdocs/modernshop` (Mac)
5. **Visit**: `http://localhost/modernshop/setup-sqlite.php`

### Option 2: WAMP (Windows Only)
**✅ All extensions included**

1. **Download WAMP**: http://www.wampserver.com/
2. **Install** and start services
3. **Copy project** to `C:\wamp64\www\modernshop`
4. **Visit**: `http://localhost/modernshop/setup-sqlite.php`

### Option 3: MAMP (Mac Only)
**✅ All extensions included**

1. **Download MAMP**: https://www.mamp.info/
2. **Install** and start servers
3. **Copy project** to `/Applications/MAMP/htdocs/modernshop`
4. **Visit**: `http://localhost/modernshop/setup-sqlite.php`

## 🛠️ Manual Installation

### Windows (Standalone PHP)

#### Method A: Download PHP with Extensions
1. **Download PHP**: https://windows.php.net/download/
   - Choose "Thread Safe" version
   - Extract to `C:\php`

2. **Add to PATH**:
   - Open System Properties → Environment Variables
   - Add `C:\php` to PATH variable
   - Restart Command Prompt

3. **Check extensions**:
   ```cmd
   php -m | findstr gd
   php -m | findstr openssl
   ```

#### Method B: Enable in Existing PHP
1. **Find php.ini**:
   ```cmd
   php --ini
   ```

2. **Edit php.ini** and uncomment (remove `;`):
   ```ini
   extension=gd
   extension=openssl
   extension=pdo_sqlite
   ```

3. **Restart web server**

### Linux (Ubuntu/Debian)

```bash
# Update package list
sudo apt update

# Install PHP and extensions
sudo apt install php php-gd php-sqlite3 php-cli

# Verify installation
php -m | grep gd
php -m | grep sqlite
php -m | grep openssl

# Restart web server (if using Apache)
sudo systemctl restart apache2
```

### Linux (CentOS/RHEL)

```bash
# Install PHP and extensions
sudo yum install php php-gd php-pdo

# For newer versions:
sudo dnf install php php-gd php-pdo

# Verify installation
php -m | grep gd
php -m | grep sqlite

# Restart web server
sudo systemctl restart httpd
```

### macOS

#### Using Homebrew:
```bash
# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install PHP (includes all extensions)
brew install php

# Verify installation
php -m | grep gd
php -m | grep openssl
```

#### Using MacPorts:
```bash
# Install PHP with extensions
sudo port install php81 +gd +openssl

# Verify installation
php -m | grep gd
```

## 🐳 Docker Solution (All Platforms)

If you have Docker installed:

```bash
# Run with Docker (all extensions included)
docker-compose up -d

# Visit: http://localhost:8000
```

Or build custom image:
```bash
docker build -t modernshop .
docker run -p 8000:80 modernshop
```

## 🔍 Verification

### Check if extensions are installed:
```bash
# Check all extensions
php -m

# Check specific extensions
php -m | grep gd
php -m | grep openssl
php -m | grep sqlite
```

### Test with PHP script:
```php
<?php
echo "GD Extension: " . (extension_loaded('gd') ? 'YES' : 'NO') . "\n";
echo "OpenSSL Extension: " . (extension_loaded('openssl') ? 'YES' : 'NO') . "\n";
echo "SQLite Extension: " . (extension_loaded('pdo_sqlite') ? 'YES' : 'NO') . "\n";
?>
```

## 🚨 Troubleshooting

### Common Issues:

1. **"Extension not found" after installation**
   - Restart web server
   - Check if you edited the correct php.ini file
   - Run `php --ini` to find the correct file

2. **Permission denied errors**
   - Run as administrator/sudo
   - Check file permissions

3. **Multiple PHP versions**
   - Check which PHP version you're using: `php --version`
   - Make sure you're editing the correct php.ini

4. **Extensions still not working**
   - Try restarting your computer
   - Check PHP error logs
   - Verify extension files exist in PHP ext/ directory

### Get Help:
- **Check requirements**: Visit `http://localhost:8000/check-requirements.php`
- **Use portable scripts**: Run `run-portable.bat` (Windows) or `run-portable.sh` (Linux/Mac)

## 🎉 Success!

Once extensions are installed:

1. **Run the setup**: `http://localhost:8000/setup-sqlite.php`
2. **Create admin account**
3. **Start using ModernShop!**

## 📞 Still Need Help?

If you're still having issues:

1. **Use XAMPP** - It's the easiest solution with everything included
2. **Check our requirements page**: `http://localhost:8000/check-requirements.php`
3. **Use Docker** if you have it installed
4. **Try the portable scripts** included in the project

The SQLite version will work with minimal extensions, so you can get started quickly and add more features later!
