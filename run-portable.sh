#!/bin/bash

echo "========================================"
echo "ModernShop Portable Server"
echo "========================================"
echo

# Check if PHP is available
if ! command -v php &> /dev/null; then
    echo "ERROR: PHP is not installed"
    echo
    echo "Please install PHP:"
    echo "Ubuntu/Debian: sudo apt install php php-sqlite3 php-gd"
    echo "CentOS/RHEL:   sudo yum install php php-pdo php-gd"
    echo "macOS:         brew install php"
    echo
    exit 1
fi

echo "PHP Version:"
php --version
echo

# Check required extensions
echo "Checking PHP extensions..."

if php -m | grep -qi "pdo"; then
    echo "✓ PDO extension found"
else
    echo "ERROR: PDO extension not found"
    exit 1
fi

if php -m | grep -qi "sqlite"; then
    echo "✓ SQLite extension found"
else
    echo "ERROR: SQLite extension not found"
    echo "Install with: sudo apt install php-sqlite3"
    exit 1
fi

if php -m | grep -qi "gd"; then
    echo "✓ GD extension found"
else
    echo "⚠ GD extension not found (image processing will be limited)"
    echo "Install with: sudo apt install php-gd"
fi

if php -m | grep -qi "openssl"; then
    echo "✓ OpenSSL extension found"
else
    echo "⚠ OpenSSL extension not found (some security features disabled)"
fi

echo
echo "Starting PHP development server..."
echo "Server will be available at: http://localhost:8000"
echo
echo "Available pages:"
echo "- Setup: http://localhost:8000/setup-sqlite.php"
echo "- Store: http://localhost:8000/"
echo "- Admin: http://localhost:8000/admin/"
echo
echo "Press Ctrl+C to stop the server"
echo "========================================"
echo

# Start PHP server
php -S localhost:8000
