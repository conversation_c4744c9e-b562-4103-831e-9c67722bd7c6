/* Advanced Form Styling */

/* Form Container */
.form-container {
    max-width: 400px;
    margin: 0 auto;
    padding: var(--space-8);
    background: var(--color-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--color-gray-200);
}

.form-header {
    text-align: center;
    margin-bottom: var(--space-8);
}

.form-header h1 {
    color: var(--color-gray-900);
    margin-bottom: var(--space-2);
}

.form-header p {
    color: var(--color-gray-600);
    margin-bottom: 0;
}

/* Form Groups with Floating Labels */
.form-group {
    position: relative;
    margin-bottom: var(--space-6);
}

.form-group.focused .form-label,
.form-group.has-value .form-label {
    transform: translateY(-12px) scale(0.85);
    color: var(--color-primary);
    background: var(--color-white);
    padding: 0 var(--space-2);
}

.form-label {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-gray-500);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    pointer-events: none;
    transition: all var(--transition-normal);
    z-index: 1;
}

.form-input {
    width: 100%;
    padding: var(--space-4);
    border: 2px solid var(--color-gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    background: var(--color-white);
    transition: all var(--transition-normal);
    position: relative;
}

.form-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input:hover {
    border-color: var(--color-gray-400);
}

/* Input States */
.form-group.error .form-input {
    border-color: var(--color-error);
}

.form-group.error .form-label {
    color: var(--color-error);
}

.form-group.success .form-input {
    border-color: var(--color-success);
}

.form-group.success .form-label {
    color: var(--color-success);
}

/* Error and Success Messages */
.form-message {
    margin-top: var(--space-2);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.form-message.error {
    color: var(--color-error);
}

.form-message.success {
    color: var(--color-success);
}

/* Password Strength Indicator */
.password-strength {
    margin-top: var(--space-2);
    height: 4px;
    background: var(--color-gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    transition: all var(--transition-normal);
    border-radius: var(--radius-full);
}

.password-strength-bar.weak {
    width: 25%;
    background: var(--color-error);
}

.password-strength-bar.fair {
    width: 50%;
    background: var(--color-warning);
}

.password-strength-bar.good {
    width: 75%;
    background: var(--color-success);
}

.password-strength-bar.strong {
    width: 100%;
    background: var(--color-success);
}

/* Form Actions */
.form-actions {
    margin-top: var(--space-8);
}

.form-actions .btn {
    width: 100%;
    justify-content: center;
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.form-divider {
    text-align: center;
    margin: var(--space-6) 0;
    position: relative;
    color: var(--color-gray-500);
}

.form-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--color-gray-300);
    z-index: 0;
}

.form-divider span {
    background: var(--color-white);
    padding: 0 var(--space-4);
    position: relative;
    z-index: 1;
}

.form-link {
    text-align: center;
    margin-top: var(--space-6);
}

.form-link a {
    color: var(--color-primary);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.form-link a:hover {
    color: var(--color-primary-dark);
    text-decoration: underline;
}

/* Checkbox and Radio Styling */
.form-checkbox,
.form-radio {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.form-checkbox input[type="checkbox"],
.form-radio input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: var(--color-primary);
}

.form-checkbox label,
.form-radio label {
    color: var(--color-gray-700);
    font-size: var(--font-size-sm);
    cursor: pointer;
    position: static;
    transform: none;
    background: none;
    padding: 0;
}

/* Select Styling */
.form-select {
    width: 100%;
    padding: var(--space-4);
    border: 2px solid var(--color-gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    background: var(--color-white);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.form-select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* File Upload Styling */
.form-file {
    position: relative;
    display: inline-block;
    width: 100%;
}

.form-file input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.form-file-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-4);
    border: 2px dashed var(--color-gray-300);
    border-radius: var(--radius-md);
    background: var(--color-gray-50);
    color: var(--color-gray-600);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.form-file:hover .form-file-label {
    border-color: var(--color-primary);
    background: var(--color-primary-50);
    color: var(--color-primary);
}

/* Loading States */
.form-loading .form-input {
    opacity: 0.7;
    pointer-events: none;
}

.form-loading .btn {
    position: relative;
    color: transparent;
}

.form-loading .btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Form Design */
@media (max-width: 768px) {
    .form-container {
        margin: var(--space-4);
        padding: var(--space-6);
        border-radius: var(--radius-lg);
    }
    
    .form-input,
    .form-select {
        padding: var(--space-3);
        font-size: var(--font-size-base);
    }
    
    .form-actions .btn {
        padding: var(--space-3) var(--space-4);
        font-size: var(--font-size-base);
    }
}

/* Auth Page Specific Styles */
.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-gray-50) 100%);
    padding: var(--space-4);
}

.auth-container {
    width: 100%;
    max-width: 450px;
}

.auth-header {
    text-align: center;
    margin-bottom: var(--space-8);
}

.auth-header .logo {
    font-size: var(--font-size-3xl);
    color: var(--color-primary);
    margin-bottom: var(--space-4);
    display: block;
}

.auth-footer {
    text-align: center;
    margin-top: var(--space-8);
    padding-top: var(--space-6);
    border-top: 1px solid var(--color-gray-200);
}

.auth-footer a {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
}

.auth-footer a:hover {
    text-decoration: underline;
}
