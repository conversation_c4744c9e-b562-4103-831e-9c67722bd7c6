<?php
$page_title = "Order Confirmation";
$page_description = "Your order has been placed successfully";
$additional_css = ['css/checkout.css', 'css/animations.css'];

require_once 'config/db.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Require login
require_login();

// Check if there's order data in session
if (!isset($_SESSION['last_order'])) {
    set_flash_message('No order information found.', 'error');
    redirect('index.php');
}

$order_data = $_SESSION['last_order'];

// Clear the order data from session after displaying
unset($_SESSION['last_order']);

include 'includes/header.php';
?>

<div class="success-container animate-fade-in-up">
    <!-- Success Icon -->
    <div class="success-icon">
        <i class="fas fa-check"></i>
    </div>
    
    <!-- Success Message -->
    <h1 class="success-title">Order Placed Successfully!</h1>
    <p class="success-message">
        Thank you for your purchase! Your order has been received and is being processed. 
        You will receive an email confirmation shortly with your order details and tracking information.
    </p>
    
    <!-- Order Details -->
    <div class="order-details-card animate-fade-in" style="animation-delay: 0.3s;">
        <h3 style="margin-bottom: var(--space-4); color: var(--color-gray-900);">
            <i class="fas fa-receipt"></i>
            Order Details
        </h3>
        
        <div class="order-detail-row">
            <span>Order Number:</span>
            <span style="font-weight: var(--font-weight-bold);">
                #<?php echo str_pad($order_data['order_ids'][0], 6, '0', STR_PAD_LEFT); ?>
            </span>
        </div>
        
        <div class="order-detail-row">
            <span>Order Date:</span>
            <span><?php echo date('F j, Y'); ?></span>
        </div>
        
        <div class="order-detail-row">
            <span>Payment Method:</span>
            <span style="text-transform: capitalize;">
                <?php echo str_replace('_', ' ', $order_data['payment_method']); ?>
            </span>
        </div>
        
        <div class="order-detail-row">
            <span>Shipping Address:</span>
            <span>
                <?php echo htmlspecialchars($order_data['shipping']['address']); ?><br>
                <?php echo htmlspecialchars($order_data['shipping']['city']); ?>, 
                <?php echo htmlspecialchars($order_data['shipping']['state']); ?> 
                <?php echo htmlspecialchars($order_data['shipping']['postal_code']); ?>
            </span>
        </div>
        
        <div class="order-detail-row">
            <span>Total Amount:</span>
            <span style="color: var(--color-primary); font-size: var(--font-size-lg);">
                <?php echo format_price($order_data['total']); ?>
            </span>
        </div>
    </div>
    
    <!-- Next Steps -->
    <div class="animate-fade-in" style="animation-delay: 0.6s; margin-top: var(--space-8);">
        <h3 style="margin-bottom: var(--space-4); color: var(--color-gray-900);">What's Next?</h3>
        
        <div style="display: grid; gap: var(--space-4); text-align: left; max-width: 500px; margin: 0 auto;">
            <div style="display: flex; align-items: center; gap: var(--space-3); padding: var(--space-4); background: var(--color-gray-50); border-radius: var(--radius-md);">
                <div style="width: 40px; height: 40px; background: var(--color-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-envelope"></i>
                </div>
                <div>
                    <div style="font-weight: var(--font-weight-semibold);">Email Confirmation</div>
                    <div style="font-size: var(--font-size-sm); color: var(--color-gray-600);">
                        You'll receive an email with order details within 5 minutes
                    </div>
                </div>
            </div>
            
            <div style="display: flex; align-items: center; gap: var(--space-3); padding: var(--space-4); background: var(--color-gray-50); border-radius: var(--radius-md);">
                <div style="width: 40px; height: 40px; background: var(--color-warning); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-box"></i>
                </div>
                <div>
                    <div style="font-weight: var(--font-weight-semibold);">Order Processing</div>
                    <div style="font-size: var(--font-size-sm); color: var(--color-gray-600);">
                        Your order will be processed within 1-2 business days
                    </div>
                </div>
            </div>
            
            <div style="display: flex; align-items: center; gap: var(--space-3); padding: var(--space-4); background: var(--color-gray-50); border-radius: var(--radius-md);">
                <div style="width: 40px; height: 40px; background: var(--color-success); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div>
                    <div style="font-weight: var(--font-weight-semibold);">Shipping & Delivery</div>
                    <div style="font-size: var(--font-size-sm); color: var(--color-gray-600);">
                        Free shipping with delivery in 3-5 business days
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="animate-fade-in" style="animation-delay: 0.9s; margin-top: var(--space-12);">
        <div style="display: flex; gap: var(--space-4); justify-content: center; flex-wrap: wrap;">
            <a href="orders.php" class="btn btn-primary btn-lg hover-lift">
                <i class="fas fa-list"></i>
                View My Orders
            </a>
            
            <a href="index.php" class="btn btn-secondary btn-lg hover-lift">
                <i class="fas fa-shopping-bag"></i>
                Continue Shopping
            </a>
        </div>
    </div>
    
    <!-- Support Info -->
    <div class="animate-fade-in" style="animation-delay: 1.2s; margin-top: var(--space-12); padding-top: var(--space-8); border-top: 1px solid var(--color-gray-200);">
        <h4 style="margin-bottom: var(--space-4); color: var(--color-gray-700);">Need Help?</h4>
        <p style="color: var(--color-gray-600); margin-bottom: var(--space-4);">
            If you have any questions about your order, please don't hesitate to contact us.
        </p>
        
        <div style="display: flex; gap: var(--space-6); justify-content: center; flex-wrap: wrap;">
            <div style="text-align: center;">
                <div style="color: var(--color-primary); font-size: var(--font-size-2xl); margin-bottom: var(--space-2);">
                    <i class="fas fa-phone"></i>
                </div>
                <div style="font-weight: var(--font-weight-semibold);">Call Us</div>
                <div style="color: var(--color-gray-600); font-size: var(--font-size-sm);">(*************</div>
            </div>
            
            <div style="text-align: center;">
                <div style="color: var(--color-primary); font-size: var(--font-size-2xl); margin-bottom: var(--space-2);">
                    <i class="fas fa-envelope"></i>
                </div>
                <div style="font-weight: var(--font-weight-semibold);">Email Us</div>
                <div style="color: var(--color-gray-600); font-size: var(--font-size-sm);"><EMAIL></div>
            </div>
            
            <div style="text-align: center;">
                <div style="color: var(--color-primary); font-size: var(--font-size-2xl); margin-bottom: var(--space-2);">
                    <i class="fas fa-comments"></i>
                </div>
                <div style="font-weight: var(--font-weight-semibold);">Live Chat</div>
                <div style="color: var(--color-gray-600); font-size: var(--font-size-sm);">Available 24/7</div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confetti effect
    createConfetti();
    
    // Auto-scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
});

function createConfetti() {
    const colors = ['#2563eb', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
    const confettiCount = 50;
    
    for (let i = 0; i < confettiCount; i++) {
        setTimeout(() => {
            const confetti = document.createElement('div');
            confetti.style.cssText = `
                position: fixed;
                top: -10px;
                left: ${Math.random() * 100}%;
                width: 10px;
                height: 10px;
                background: ${colors[Math.floor(Math.random() * colors.length)]};
                border-radius: 50%;
                pointer-events: none;
                z-index: 9999;
                animation: confettiFall 3s linear forwards;
            `;
            
            document.body.appendChild(confetti);
            
            setTimeout(() => {
                confetti.remove();
            }, 3000);
        }, i * 100);
    }
}

// Add confetti animation
const style = document.createElement('style');
style.textContent = `
    @keyframes confettiFall {
        0% {
            transform: translateY(-100vh) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(100vh) rotate(720deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>

<?php include 'includes/footer.php'; ?>
