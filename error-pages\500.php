<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Error - ModernShop</title>
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--color-error-50) 0%, var(--color-error-100) 100%);
            padding: var(--space-8);
        }
        
        .error-container {
            text-align: center;
            max-width: 600px;
            background: var(--color-white);
            padding: var(--space-12);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-2xl);
        }
        
        .error-icon {
            font-size: 6rem;
            color: var(--color-error);
            margin-bottom: var(--space-6);
            animation: pulse 2s infinite;
        }
        
        .error-code {
            font-size: 4rem;
            font-weight: var(--font-weight-bold);
            color: var(--color-error);
            line-height: 1;
            margin-bottom: var(--space-4);
        }
        
        .error-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-gray-900);
            margin-bottom: var(--space-4);
        }
        
        .error-message {
            font-size: var(--font-size-lg);
            color: var(--color-gray-600);
            margin-bottom: var(--space-8);
            line-height: var(--line-height-relaxed);
        }
        
        .error-actions {
            display: flex;
            gap: var(--space-4);
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-4);
            background: var(--color-gray-100);
            border-radius: var(--radius-full);
            font-size: var(--font-size-sm);
            color: var(--color-gray-700);
            margin-top: var(--space-6);
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            background: var(--color-error);
            border-radius: 50%;
            animation: blink 1.5s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: var(--color-gray-200);
            border-radius: var(--radius-full);
            overflow: hidden;
            margin-top: var(--space-6);
        }
        
        .progress-fill {
            height: 100%;
            background: var(--color-primary);
            border-radius: var(--radius-full);
            animation: progress 3s ease-in-out infinite;
        }
        
        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 0%; }
        }
        
        @media (max-width: 768px) {
            .error-icon {
                font-size: 4rem;
            }
            
            .error-code {
                font-size: 3rem;
            }
            
            .error-title {
                font-size: var(--font-size-2xl);
            }
            
            .error-message {
                font-size: var(--font-size-base);
            }
            
            .error-container {
                padding: var(--space-8);
            }
            
            .error-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="error-page">
        <div class="error-container animate-fade-in-up">
            <div class="error-icon animate-fade-in">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <div class="error-code animate-fade-in" style="animation-delay: 0.2s;">500</div>
            
            <h1 class="error-title animate-fade-in" style="animation-delay: 0.4s;">
                Internal Server Error
            </h1>
            
            <p class="error-message animate-fade-in" style="animation-delay: 0.6s;">
                Oops! Something went wrong on our end. Our technical team has been notified and is working to fix this issue. 
                Please try again in a few moments.
            </p>
            
            <div class="status-indicator animate-fade-in" style="animation-delay: 0.8s;">
                <div class="status-dot"></div>
                <span>Our team is investigating this issue</span>
            </div>
            
            <div class="progress-bar animate-fade-in" style="animation-delay: 1s;">
                <div class="progress-fill"></div>
            </div>
            
            <div class="error-actions animate-fade-in" style="animation-delay: 1.2s;">
                <button onclick="location.reload()" class="btn btn-primary btn-lg hover-lift">
                    <i class="fas fa-redo"></i>
                    Try Again
                </button>
                
                <a href="/" class="btn btn-secondary btn-lg hover-lift">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
                
                <button onclick="history.back()" class="btn btn-outline btn-lg hover-lift">
                    <i class="fas fa-arrow-left"></i>
                    Go Back
                </button>
            </div>
            
            <!-- Technical Details (for development) -->
            <?php if (defined('ENVIRONMENT') && ENVIRONMENT === 'development'): ?>
                <div style="margin-top: var(--space-12); padding-top: var(--space-8); border-top: 1px solid var(--color-gray-200); text-align: left;">
                    <h3 style="color: var(--color-gray-700); margin-bottom: var(--space-4);">
                        Technical Details (Development Mode):
                    </h3>
                    <div style="background: var(--color-gray-100); padding: var(--space-4); border-radius: var(--radius-md); font-family: monospace; font-size: var(--font-size-sm); color: var(--color-gray-800);">
                        <strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                        <strong>URL:</strong> <?php echo $_SERVER['REQUEST_URI'] ?? 'Unknown'; ?><br>
                        <strong>Method:</strong> <?php echo $_SERVER['REQUEST_METHOD'] ?? 'Unknown'; ?><br>
                        <strong>User Agent:</strong> <?php echo substr($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown', 0, 100); ?><br>
                        <strong>IP:</strong> <?php echo $_SERVER['REMOTE_ADDR'] ?? 'Unknown'; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Support Information -->
            <div style="margin-top: var(--space-8); padding-top: var(--space-6); border-top: 1px solid var(--color-gray-200);">
                <h3 style="color: var(--color-gray-700); margin-bottom: var(--space-4);">
                    Need Immediate Help?
                </h3>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-4); margin-bottom: var(--space-6);">
                    <div style="text-align: center; padding: var(--space-4); background: var(--color-gray-50); border-radius: var(--radius-md);">
                        <i class="fas fa-envelope" style="font-size: var(--font-size-xl); color: var(--color-primary); margin-bottom: var(--space-2);"></i>
                        <div style="font-weight: var(--font-weight-semibold); margin-bottom: var(--space-1);">Email Support</div>
                        <a href="mailto:<EMAIL>" class="text-primary hover:text-primary-dark">
                            <EMAIL>
                        </a>
                    </div>
                    
                    <div style="text-align: center; padding: var(--space-4); background: var(--color-gray-50); border-radius: var(--radius-md);">
                        <i class="fas fa-phone" style="font-size: var(--font-size-xl); color: var(--color-primary); margin-bottom: var(--space-2);"></i>
                        <div style="font-weight: var(--font-weight-semibold); margin-bottom: var(--space-1);">Phone Support</div>
                        <a href="tel:+15551234567" class="text-primary hover:text-primary-dark">
                            (*************
                        </a>
                    </div>
                    
                    <div style="text-align: center; padding: var(--space-4); background: var(--color-gray-50); border-radius: var(--radius-md);">
                        <i class="fas fa-comments" style="font-size: var(--font-size-xl); color: var(--color-primary); margin-bottom: var(--space-2);"></i>
                        <div style="font-weight: var(--font-weight-semibold); margin-bottom: var(--space-1);">Live Chat</div>
                        <button onclick="openLiveChat()" class="text-primary hover:text-primary-dark" style="background: none; border: none; cursor: pointer;">
                            Start Chat
                        </button>
                    </div>
                </div>
                
                <p style="color: var(--color-gray-600); font-size: var(--font-size-sm); text-align: center;">
                    Error ID: <?php echo uniqid('ERR_'); ?> | 
                    Time: <?php echo date('Y-m-d H:i:s T'); ?>
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-retry functionality
        let retryCount = 0;
        const maxRetries = 3;
        
        function autoRetry() {
            if (retryCount < maxRetries) {
                retryCount++;
                console.log(`Auto-retry attempt ${retryCount}/${maxRetries}`);
                
                setTimeout(() => {
                    location.reload();
                }, 5000 * retryCount); // Exponential backoff
            }
        }
        
        // Check if this is an automatic retry
        const urlParams = new URLSearchParams(window.location.search);
        if (!urlParams.has('manual')) {
            setTimeout(autoRetry, 3000);
        }
        
        // Live chat function
        function openLiveChat() {
            // This would integrate with your live chat service
            alert('Live chat would open here. Please use email or phone support for now.');
        }
        
        // Add some interactive feedback
        document.addEventListener('DOMContentLoaded', function() {
            // Update status message periodically
            const statusMessages = [
                'Our team is investigating this issue',
                'Checking server status...',
                'Analyzing error logs...',
                'Implementing fix...',
                'Testing solution...'
            ];
            
            let messageIndex = 0;
            const statusText = document.querySelector('.status-indicator span');
            
            setInterval(() => {
                messageIndex = (messageIndex + 1) % statusMessages.length;
                statusText.textContent = statusMessages[messageIndex];
            }, 3000);
            
            // Add click tracking for error reporting
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // Track user actions for error analysis
                    console.log('User action:', this.textContent.trim());
                });
            });
        });
        
        // Report error to analytics (if available)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'exception', {
                description: '500 Internal Server Error',
                fatal: true
            });
        }
    </script>
</body>
</html>
