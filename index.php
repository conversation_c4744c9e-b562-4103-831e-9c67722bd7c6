<?php
$page_title = "Home";
$page_description = "Welcome to ModernShop - Your premier destination for quality products at great prices";

include 'includes/header.php';

// Get categories for filtering
try {
    $categories_stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $categories_stmt->fetchAll();
} catch (Exception $e) {
    $categories = [];
}

// Get products with optional category filtering
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$search_query = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';

try {
    $sql = "SELECT p.*, c.name as category_name FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE 1=1";
    $params = [];
    
    if ($category_filter > 0) {
        $sql .= " AND p.category_id = ?";
        $params[] = $category_filter;
    }
    
    if (!empty($search_query)) {
        $sql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
        $params[] = "%$search_query%";
        $params[] = "%$search_query%";
    }
    
    $sql .= " ORDER BY p.id DESC LIMIT 20";
    
    $products_stmt = $pdo->prepare($sql);
    $products_stmt->execute($params);
    $products = $products_stmt->fetchAll();
} catch (Exception $e) {
    $products = [];
    set_flash_message("Error loading products: " . $e->getMessage(), 'error');
}
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <h1>Welcome to ModernShop</h1>
        <p class="lead">Discover amazing products at unbeatable prices. Shop with confidence and enjoy fast, reliable delivery.</p>
        <div style="margin-top: var(--space-8);">
            <a href="#products" class="btn btn-primary btn-lg">
                <i class="fas fa-shopping-bag"></i>
                Shop Now
            </a>
            <a href="#about" class="btn btn-secondary btn-lg" style="margin-left: var(--space-4);">
                <i class="fas fa-info-circle"></i>
                Learn More
            </a>
        </div>
    </div>
</section>

<!-- Search and Filter Section -->
<section class="container" style="margin-top: var(--space-12);">
    <div class="flex flex-col md:flex-row gap-6 items-center justify-between">
        <!-- Search Form -->
        <div class="flex-1" style="max-width: 400px;">
            <form method="GET" class="flex gap-2">
                <input type="hidden" name="category" value="<?php echo $category_filter; ?>">
                <input 
                    type="text" 
                    name="search" 
                    placeholder="Search products..." 
                    value="<?php echo htmlspecialchars($search_query); ?>"
                    class="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    style="padding: var(--space-3) var(--space-4); border: 1px solid var(--color-gray-300); border-radius: var(--radius-md);"
                >
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        
        <!-- Category Filter -->
        <div class="flex gap-2 flex-wrap">
            <a href="index.php" class="btn <?php echo $category_filter == 0 ? 'btn-primary' : 'btn-secondary'; ?> btn-sm">
                All Products
            </a>
            <?php foreach ($categories as $category): ?>
                <a href="?category=<?php echo $category['id']; ?><?php echo $search_query ? '&search=' . urlencode($search_query) : ''; ?>" 
                   class="btn <?php echo $category_filter == $category['id'] ? 'btn-primary' : 'btn-secondary'; ?> btn-sm">
                    <?php echo htmlspecialchars($category['name']); ?>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Products Section -->
<section id="products" class="container" style="margin-top: var(--space-12);">
    <div class="section-header">
        <h2 class="section-title">
            <?php if ($category_filter > 0): ?>
                <?php
                $current_category = array_filter($categories, function($cat) use ($category_filter) {
                    return $cat['id'] == $category_filter;
                });
                $current_category = reset($current_category);
                echo htmlspecialchars($current_category['name'] ?? 'Category');
                ?>
            <?php elseif (!empty($search_query)): ?>
                Search Results for "<?php echo htmlspecialchars($search_query); ?>"
            <?php else: ?>
                Featured Products
            <?php endif; ?>
        </h2>
        <p class="section-subtitle">
            <?php if (empty($products)): ?>
                No products found. Try adjusting your search or filter criteria.
            <?php else: ?>
                Discover our carefully curated selection of high-quality products
            <?php endif; ?>
        </p>
    </div>
    
    <?php if (!empty($products)): ?>
        <div class="products-grid">
            <?php foreach ($products as $product): ?>
                <div class="product-card">
                    <div class="product-card-image-container" style="position: relative; overflow: hidden;">
                        <img 
                            src="<?php echo get_product_image($product['image']); ?>" 
                            alt="<?php echo htmlspecialchars($product['name']); ?>"
                            class="product-card-image"
                            style="transition: transform var(--transition-normal);"
                            onmouseover="this.style.transform='scale(1.05)'"
                            onmouseout="this.style.transform='scale(1)'"
                        >
                        
                        <?php if ($product['stock'] <= 5 && $product['stock'] > 0): ?>
                            <div class="badge badge-warning" style="position: absolute; top: var(--space-2); right: var(--space-2);">
                                Low Stock
                            </div>
                        <?php elseif ($product['stock'] == 0): ?>
                            <div class="badge badge-error" style="position: absolute; top: var(--space-2); right: var(--space-2);">
                                Out of Stock
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-card-content">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="product-card-title">
                                <?php echo htmlspecialchars($product['name']); ?>
                            </h3>
                            <?php if (!empty($product['category_name'])): ?>
                                <span class="badge badge-primary" style="font-size: var(--font-size-xs);">
                                    <?php echo htmlspecialchars($product['category_name']); ?>
                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <p class="text-gray-600 mb-3" style="font-size: var(--font-size-sm); line-height: var(--line-height-relaxed);">
                            <?php echo htmlspecialchars(truncate_text($product['description'], 80)); ?>
                        </p>
                        
                        <div class="flex justify-between items-center">
                            <div class="product-card-price">
                                <?php echo format_price($product['price']); ?>
                            </div>
                            
                            <div class="flex gap-2">
                                <a href="product.php?id=<?php echo $product['id']; ?>" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-eye"></i>
                                    View
                                </a>
                                
                                <?php if ($product['stock'] > 0 && !is_admin()): ?>
                                    <button 
                                        class="btn btn-primary btn-sm add-to-cart" 
                                        data-product-id="<?php echo $product['id']; ?>"
                                        data-quantity="1"
                                    >
                                        <i class="fas fa-cart-plus"></i>
                                        Add to Cart
                                    </button>
                                <?php elseif ($product['stock'] == 0): ?>
                                    <button class="btn btn-secondary btn-sm" disabled>
                                        <i class="fas fa-times"></i>
                                        Out of Stock
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if ($product['stock'] > 0 && $product['stock'] <= 10): ?>
                            <div class="mt-2" style="font-size: var(--font-size-xs); color: var(--color-warning-600);">
                                <i class="fas fa-exclamation-triangle"></i>
                                Only <?php echo $product['stock']; ?> left in stock
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="text-center" style="padding: var(--space-16) 0;">
            <i class="fas fa-search" style="font-size: var(--font-size-5xl); color: var(--color-gray-400); margin-bottom: var(--space-4);"></i>
            <h3 style="color: var(--color-gray-600); margin-bottom: var(--space-4);">No Products Found</h3>
            <p style="color: var(--color-gray-500); margin-bottom: var(--space-6);">
                Try adjusting your search criteria or browse all products.
            </p>
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i>
                View All Products
            </a>
        </div>
    <?php endif; ?>
</section>

<!-- About Section -->
<section id="about" class="container" style="margin-top: var(--space-20); margin-bottom: var(--space-12);">
    <div class="section-header">
        <h2 class="section-title">Why Choose ModernShop?</h2>
        <p class="section-subtitle">We're committed to providing you with the best shopping experience</p>
    </div>
    
    <div class="grid md:grid-cols-3 gap-8">
        <div class="text-center">
            <div style="font-size: var(--font-size-5xl); color: var(--color-primary); margin-bottom: var(--space-4);">
                <i class="fas fa-shipping-fast"></i>
            </div>
            <h3 style="margin-bottom: var(--space-3);">Fast Delivery</h3>
            <p style="color: var(--color-gray-600);">Quick and reliable shipping to get your products to you as soon as possible.</p>
        </div>
        
        <div class="text-center">
            <div style="font-size: var(--font-size-5xl); color: var(--color-primary); margin-bottom: var(--space-4);">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h3 style="margin-bottom: var(--space-3);">Secure Shopping</h3>
            <p style="color: var(--color-gray-600);">Your personal information and payments are always protected and secure.</p>
        </div>
        
        <div class="text-center">
            <div style="font-size: var(--font-size-5xl); color: var(--color-primary); margin-bottom: var(--space-4);">
                <i class="fas fa-headset"></i>
            </div>
            <h3 style="margin-bottom: var(--space-3);">24/7 Support</h3>
            <p style="color: var(--color-gray-600);">Our customer support team is always here to help you with any questions.</p>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
