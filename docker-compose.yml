version: '3.8'

services:
  web:
    image: php:8.1-apache
    container_name: modernshop_web
    ports:
      - "8000:80"
    volumes:
      - .:/var/www/html
    depends_on:
      - db
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html
    command: >
      bash -c "
      docker-php-ext-install pdo pdo_mysql gd &&
      a2enmod rewrite &&
      apache2-foreground
      "

  db:
    image: mysql:8.0
    container_name: modernshop_db
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: modernshop
      MYSQL_USER: modernshop
      MYSQL_PASSWORD: modernshop
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database:/docker-entrypoint-initdb.d

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: modernshop_phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: db
      PMA_USER: root
      PMA_PASSWORD: root
    depends_on:
      - db

volumes:
  mysql_data:
