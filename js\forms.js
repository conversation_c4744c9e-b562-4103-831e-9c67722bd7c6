// Form Enhancement JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeFormEnhancements();
    initializePasswordStrength();
    initializeFormValidation();
});

// Enhanced form functionality
function initializeFormEnhancements() {
    // Floating label functionality
    const formGroups = document.querySelectorAll('.form-group');
    
    formGroups.forEach(group => {
        const input = group.querySelector('.form-input, .form-select');
        const label = group.querySelector('.form-label');
        
        if (input && label) {
            // Check initial state
            checkInputValue(group, input);
            
            // Focus events
            input.addEventListener('focus', () => {
                group.classList.add('focused');
            });
            
            input.addEventListener('blur', () => {
                group.classList.remove('focused');
                checkInputValue(group, input);
            });
            
            // Input events
            input.addEventListener('input', () => {
                checkInputValue(group, input);
                clearFieldError(group);
            });
        }
    });
}

// Check if input has value and update classes
function checkInputValue(group, input) {
    if (input.value.trim() !== '') {
        group.classList.add('has-value');
    } else {
        group.classList.remove('has-value');
    }
}

// Password strength indicator
function initializePasswordStrength() {
    const passwordInputs = document.querySelectorAll('input[type="password"][data-strength]');
    
    passwordInputs.forEach(input => {
        const strengthContainer = createPasswordStrengthIndicator();
        input.parentNode.appendChild(strengthContainer);
        
        input.addEventListener('input', function() {
            updatePasswordStrength(this.value, strengthContainer);
        });
    });
}

// Create password strength indicator HTML
function createPasswordStrengthIndicator() {
    const container = document.createElement('div');
    container.className = 'password-strength';
    
    const bar = document.createElement('div');
    bar.className = 'password-strength-bar';
    
    const text = document.createElement('div');
    text.className = 'password-strength-text';
    text.style.cssText = `
        font-size: var(--font-size-xs);
        margin-top: var(--space-1);
        color: var(--color-gray-600);
    `;
    
    container.appendChild(bar);
    container.appendChild(text);
    
    return container;
}

// Update password strength indicator
function updatePasswordStrength(password, container) {
    const bar = container.querySelector('.password-strength-bar');
    const text = container.querySelector('.password-strength-text');
    
    if (!password) {
        bar.className = 'password-strength-bar';
        text.textContent = '';
        return;
    }
    
    const strength = calculatePasswordStrength(password);
    
    bar.className = `password-strength-bar ${strength.level}`;
    text.textContent = `Password strength: ${strength.level.charAt(0).toUpperCase() + strength.level.slice(1)}`;
    
    if (strength.feedback.length > 0) {
        text.textContent += ` - ${strength.feedback[0]}`;
    }
}

// Calculate password strength
function calculatePasswordStrength(password) {
    let strength = 0;
    const feedback = [];
    
    // Length check
    if (password.length >= 8) {
        strength += 25;
    } else {
        feedback.push('Use at least 8 characters');
    }
    
    // Lowercase check
    if (/[a-z]/.test(password)) {
        strength += 25;
    } else {
        feedback.push('Include lowercase letters');
    }
    
    // Uppercase check
    if (/[A-Z]/.test(password)) {
        strength += 25;
    } else {
        feedback.push('Include uppercase letters');
    }
    
    // Number or special character check
    if (/[0-9]/.test(password) || /[^a-zA-Z0-9]/.test(password)) {
        strength += 25;
    } else {
        feedback.push('Include numbers or special characters');
    }
    
    // Determine level
    let level;
    if (strength < 50) {
        level = 'weak';
    } else if (strength < 75) {
        level = 'fair';
    } else if (strength < 100) {
        level = 'good';
    } else {
        level = 'strong';
    }
    
    return { strength, level, feedback };
}

// Form validation
function initializeFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                return false;
            }
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('.form-input, .form-select');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    });
}

// Validate entire form
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('.form-input, .form-select');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    // Special validation for password confirmation
    const password = form.querySelector('input[name="password"]');
    const confirmPassword = form.querySelector('input[name="confirm_password"]');
    
    if (password && confirmPassword) {
        if (password.value !== confirmPassword.value) {
            showFieldError(confirmPassword.closest('.form-group'), 'Passwords do not match');
            isValid = false;
        }
    }
    
    return isValid;
}

// Validate individual field
function validateField(input) {
    const group = input.closest('.form-group');
    const value = input.value.trim();
    const required = input.hasAttribute('required');
    const type = input.type;
    const name = input.name;
    
    // Clear previous errors
    clearFieldError(group);
    
    // Required field check
    if (required && !value) {
        showFieldError(group, 'This field is required');
        return false;
    }
    
    // Skip further validation if field is empty and not required
    if (!value && !required) {
        return true;
    }
    
    // Email validation
    if (type === 'email' || name === 'email') {
        if (!isValidEmail(value)) {
            showFieldError(group, 'Please enter a valid email address');
            return false;
        }
    }
    
    // Username validation
    if (name === 'username') {
        if (value.length < 3) {
            showFieldError(group, 'Username must be at least 3 characters');
            return false;
        }
        if (!/^[a-zA-Z0-9_]+$/.test(value)) {
            showFieldError(group, 'Username can only contain letters, numbers, and underscores');
            return false;
        }
    }
    
    // Password validation
    if (type === 'password' && name === 'password') {
        if (value.length < 6) {
            showFieldError(group, 'Password must be at least 6 characters');
            return false;
        }
    }
    
    // Full name validation
    if (name === 'full_name') {
        if (value.length < 2) {
            showFieldError(group, 'Full name must be at least 2 characters');
            return false;
        }
    }
    
    return true;
}

// Show field error
function showFieldError(group, message) {
    group.classList.add('error');
    group.classList.remove('success');
    
    // Remove existing error message
    const existingError = group.querySelector('.form-message.error');
    if (existingError) {
        existingError.remove();
    }
    
    // Add new error message
    const errorElement = document.createElement('div');
    errorElement.className = 'form-message error form-error-enter';
    errorElement.textContent = message;
    
    const input = group.querySelector('.form-input, .form-select');
    input.parentNode.appendChild(errorElement);
}

// Clear field error
function clearFieldError(group) {
    group.classList.remove('error');
    
    const errorMessage = group.querySelector('.form-message.error');
    if (errorMessage) {
        errorMessage.remove();
    }
}

// Show field success
function showFieldSuccess(group) {
    group.classList.add('success');
    group.classList.remove('error');
    
    clearFieldError(group);
}

// Email validation helper
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Form submission with loading state
function submitFormWithLoading(form, callback) {
    const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
    const originalText = submitButton.textContent || submitButton.value;
    
    // Add loading state
    form.classList.add('form-loading');
    submitButton.disabled = true;
    
    if (submitButton.tagName === 'BUTTON') {
        submitButton.innerHTML = '<span class="loading-dots"><span></span><span></span><span></span></span> Processing...';
    } else {
        submitButton.value = 'Processing...';
    }
    
    // Execute callback
    if (callback) {
        callback().finally(() => {
            // Remove loading state
            form.classList.remove('form-loading');
            submitButton.disabled = false;
            
            if (submitButton.tagName === 'BUTTON') {
                submitButton.textContent = originalText;
            } else {
                submitButton.value = originalText;
            }
        });
    }
}

// Auto-resize textarea
function initializeAutoResizeTextarea() {
    const textareas = document.querySelectorAll('textarea[data-auto-resize]');
    
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
        
        // Initial resize
        textarea.style.height = textarea.scrollHeight + 'px';
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeAutoResizeTextarea();
});
