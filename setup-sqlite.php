<?php
/**
 * ModernShop SQLite Setup Script
 * Simplified setup using SQLite database
 */

// Check if setup has already been run
if (file_exists('setup_complete.txt')) {
    die('Setup has already been completed. Delete setup_complete.txt to run again.');
}

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    die('PHP 7.4 or higher is required. Current version: ' . PHP_VERSION);
}

// Check required extensions (reduced list for SQLite)
$required_extensions = ['pdo', 'pdo_sqlite'];
$optional_extensions = ['gd', 'openssl', 'json'];
$missing_required = [];
$missing_optional = [];

foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $missing_required[] = $ext;
    }
}

foreach ($optional_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $missing_optional[] = $ext;
    }
}

if (!empty($missing_required)) {
    die('Missing required PHP extensions: ' . implode(', ', $missing_required));
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ModernShop SQLite Setup</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; }
        .container { max-width: 600px; margin: 50px auto; padding: 20px; }
        .card { background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 40px; }
        h1 { color: #1e293b; margin-bottom: 10px; font-size: 2rem; }
        .subtitle { color: #64748b; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 600; color: #374151; }
        input { width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; }
        input:focus { outline: none; border-color: #3b82f6; }
        .btn { background: #3b82f6; color: white; padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: 600; width: 100%; }
        .btn:hover { background: #2563eb; }
        .success { background: #10b981; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .error { background: #ef4444; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .warning { background: #f59e0b; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .info { background: #3b82f6; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .feature-list { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .feature-list h3 { color: #1e293b; margin-bottom: 15px; }
        .feature-list ul { margin-left: 20px; }
        .feature-list li { margin-bottom: 8px; color: #374151; }
        .check { color: #10b981; font-weight: bold; }
        .cross { color: #ef4444; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🛍️ ModernShop SQLite Setup</h1>
            <p class="subtitle">Simplified setup using SQLite database - no MySQL required!</p>

            <div class="info">
                <strong>🎉 SQLite Benefits:</strong><br>
                ✅ No database server setup required<br>
                ✅ File-based database (portable)<br>
                ✅ Perfect for development and small projects<br>
                ✅ Zero configuration needed
            </div>

            <?php if (!empty($missing_optional)): ?>
                <div class="warning">
                    <strong>⚠️ Optional Extensions Missing:</strong><br>
                    <?php echo implode(', ', $missing_optional); ?><br><br>
                    <strong>Impact:</strong><br>
                    <?php if (in_array('gd', $missing_optional)): ?>
                    • Image processing features will be limited<br>
                    <?php endif; ?>
                    <?php if (in_array('openssl', $missing_optional)): ?>
                    • Some security features may not work<br>
                    <?php endif; ?>
                    <em>The application will still work, but with reduced functionality.</em>
                </div>
            <?php endif; ?>

            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $admin_name = $_POST['admin_name'] ?? '';
                $admin_username = $_POST['admin_username'] ?? '';
                $admin_email = $_POST['admin_email'] ?? '';
                $admin_password = $_POST['admin_password'] ?? '';

                try {
                    // Include SQLite database configuration
                    require_once 'config/db-sqlite.php';

                    echo '<div class="success">✅ SQLite database connection successful!</div>';

                    // Read and execute SQLite schema
                    if (file_exists('database/schema-sqlite.sql')) {
                        $schema = file_get_contents('database/schema-sqlite.sql');
                        
                        // Split by semicolon and execute each statement
                        $statements = array_filter(array_map('trim', explode(';', $schema)));
                        
                        foreach ($statements as $statement) {
                            if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                                try {
                                    $pdo->exec($statement);
                                } catch (Exception $e) {
                                    // Continue if statement fails (might be duplicate)
                                    error_log("SQLite setup warning: " . $e->getMessage());
                                }
                            }
                        }
                        echo '<div class="success">✅ Database schema created with sample data!</div>';
                    }

                    // Create admin user
                    if (!empty($admin_name) && !empty($admin_username) && !empty($admin_email) && !empty($admin_password)) {
                        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                        
                        $stmt = $pdo->prepare("INSERT OR REPLACE INTO users (full_name, username, email, password, role) VALUES (?, ?, ?, ?, 'admin')");
                        $stmt->execute([$admin_name, $admin_username, $admin_email, $hashed_password]);
                        
                        echo '<div class="success">✅ Admin user created successfully!</div>';
                    }

                    // Update main database configuration to use SQLite
                    $config_content = file_get_contents('config/db-sqlite.php');
                    file_put_contents('config/db.php', $config_content);
                    echo '<div class="success">✅ Database configuration updated to use SQLite!</div>';

                    // Create necessary directories
                    $directories = ['uploads', 'uploads/products', 'uploads/users', 'cache', 'logs'];
                    foreach ($directories as $dir) {
                        if (!is_dir($dir)) {
                            mkdir($dir, 0755, true);
                        }
                    }
                    echo '<div class="success">✅ Required directories created!</div>';

                    // Mark setup as complete
                    file_put_contents('setup_complete.txt', date('Y-m-d H:i:s') . ' - SQLite Setup');

                    echo '<div class="success">
                        <h3>🎉 SQLite Setup Complete!</h3>
                        <p>Your ModernShop installation is ready with SQLite database!</p>
                        <br>
                        <strong>Database Location:</strong> database/modernshop.db<br>
                        <strong>Sample Data:</strong> 10 products across 5 categories included<br>
                        <br>
                        <strong>Next Steps:</strong><br>
                        1. <a href="index.php" style="color: white; text-decoration: underline;">Visit your store</a><br>
                        2. <a href="admin/" style="color: white; text-decoration: underline;">Access admin panel</a><br>
                        3. Delete setup files for security (setup.php, setup-sqlite.php)
                    </div>';

                } catch (Exception $e) {
                    echo '<div class="error">❌ Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            } else {
            ?>

            <div class="feature-list">
                <h3>📋 System Check</h3>
                <ul>
                    <li><span class="check">✅</span> PHP <?php echo PHP_VERSION; ?> (7.4+ required)</li>
                    <li><span class="check">✅</span> PDO extension</li>
                    <li><span class="check">✅</span> PDO SQLite driver</li>
                    <?php foreach ($optional_extensions as $ext): ?>
                        <li><span class="<?php echo extension_loaded($ext) ? 'check">✅' : 'cross">❌'; ?></span> <?php echo $ext; ?> extension <?php echo extension_loaded($ext) ? '' : '(optional)'; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <form method="POST">
                <h3 style="margin-bottom: 20px; color: #374151;">Create Admin User</h3>
                
                <div class="form-group">
                    <label>Full Name:</label>
                    <input type="text" name="admin_name" placeholder="John Doe" required>
                </div>
                
                <div class="form-group">
                    <label>Username:</label>
                    <input type="text" name="admin_username" placeholder="admin" required>
                </div>
                
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" name="admin_email" placeholder="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" name="admin_password" placeholder="Strong password" required>
                </div>

                <button type="submit" class="btn">🚀 Setup ModernShop with SQLite</button>
            </form>

            <div class="feature-list" style="margin-top: 30px;">
                <h3>🎁 What's Included</h3>
                <ul>
                    <li>Complete e-commerce platform</li>
                    <li>5 product categories</li>
                    <li>10 sample products</li>
                    <li>User authentication system</li>
                    <li>Shopping cart & checkout</li>
                    <li>Admin panel</li>
                    <li>Search & filtering</li>
                    <li>Responsive design</li>
                    <li>Analytics tracking</li>
                    <li>Security features</li>
                </ul>
            </div>

            <?php } ?>
        </div>
    </div>
</body>
</html>
