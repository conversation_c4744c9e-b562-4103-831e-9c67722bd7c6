// Checkout Process JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeCheckout();
});

let currentStep = 1;
const totalSteps = 3;

function initializeCheckout() {
    // Initialize step navigation
    initializeStepNavigation();
    
    // Initialize form validation
    initializeCheckoutValidation();
    
    // Initialize payment methods
    initializePaymentMethods();
    
    // Initialize form auto-save
    initializeFormAutoSave();
    
    // Show first step
    showStep(1);
}

// Step Navigation
function initializeStepNavigation() {
    const nextButtons = document.querySelectorAll('.btn-next');
    const prevButtons = document.querySelectorAll('.btn-prev');
    
    nextButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            if (validateCurrentStep()) {
                nextStep();
            }
        });
    });
    
    prevButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            prevStep();
        });
    });
}

function showStep(step) {
    // Hide all steps
    document.querySelectorAll('.checkout-step').forEach(stepEl => {
        stepEl.classList.remove('active');
    });
    
    // Show current step
    const currentStepEl = document.querySelector(`#step-${step}`);
    if (currentStepEl) {
        currentStepEl.classList.add('active');
    }
    
    // Update progress indicators
    updateProgressIndicators(step);
    
    // Update navigation buttons
    updateNavigationButtons(step);
    
    currentStep = step;
}

function updateProgressIndicators(step) {
    document.querySelectorAll('.progress-step').forEach((stepEl, index) => {
        const stepNumber = index + 1;
        
        stepEl.classList.remove('active', 'completed');
        
        if (stepNumber === step) {
            stepEl.classList.add('active');
        } else if (stepNumber < step) {
            stepEl.classList.add('completed');
            
            // Add checkmark to completed steps
            const circle = stepEl.querySelector('.progress-step-circle');
            if (circle && !circle.querySelector('.fa-check')) {
                circle.innerHTML = '<i class="fas fa-check"></i>';
            }
        } else {
            // Reset future steps
            const circle = stepEl.querySelector('.progress-step-circle');
            if (circle) {
                circle.textContent = stepNumber;
            }
        }
    });
}

function updateNavigationButtons(step) {
    const prevBtn = document.querySelector('.btn-prev');
    const nextBtn = document.querySelector('.btn-next');
    const submitBtn = document.querySelector('.btn-submit');
    
    if (prevBtn) {
        prevBtn.style.display = step === 1 ? 'none' : 'inline-flex';
    }
    
    if (nextBtn) {
        nextBtn.style.display = step === totalSteps ? 'none' : 'inline-flex';
    }
    
    if (submitBtn) {
        submitBtn.style.display = step === totalSteps ? 'inline-flex' : 'none';
    }
}

function nextStep() {
    if (currentStep < totalSteps) {
        showStep(currentStep + 1);
        scrollToTop();
    }
}

function prevStep() {
    if (currentStep > 1) {
        showStep(currentStep - 1);
        scrollToTop();
    }
}

function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Form Validation
function initializeCheckoutValidation() {
    const forms = document.querySelectorAll('.checkout-form form');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    });
}

function validateCurrentStep() {
    const currentStepEl = document.querySelector(`#step-${currentStep}`);
    if (!currentStepEl) return true;
    
    const requiredFields = currentStepEl.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    // Step-specific validation
    if (currentStep === 2) {
        isValid = validateShippingStep() && isValid;
    } else if (currentStep === 3) {
        isValid = validatePaymentStep() && isValid;
    }
    
    if (!isValid) {
        showNotification('Please fill in all required fields correctly', 'error');
    }
    
    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const fieldName = field.name;
    
    // Clear previous errors
    clearFieldError(field);
    
    // Required field check
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'This field is required');
        return false;
    }
    
    // Skip further validation if field is empty and not required
    if (!value && !field.hasAttribute('required')) {
        return true;
    }
    
    // Email validation
    if (fieldType === 'email' || fieldName === 'email') {
        if (!isValidEmail(value)) {
            showFieldError(field, 'Please enter a valid email address');
            return false;
        }
    }
    
    // Phone validation
    if (fieldName === 'phone') {
        if (!/^\+?[\d\s\-\(\)]+$/.test(value) || value.length < 10) {
            showFieldError(field, 'Please enter a valid phone number');
            return false;
        }
    }
    
    // Postal code validation
    if (fieldName === 'postal_code') {
        if (!/^[\d\w\s\-]+$/.test(value)) {
            showFieldError(field, 'Please enter a valid postal code');
            return false;
        }
    }
    
    return true;
}

function validateShippingStep() {
    // Additional shipping validation if needed
    return true;
}

function validatePaymentStep() {
    const selectedPayment = document.querySelector('input[name="payment_method"]:checked');
    
    if (!selectedPayment) {
        showNotification('Please select a payment method', 'error');
        return false;
    }
    
    // Credit card validation
    if (selectedPayment.value === 'credit_card') {
        const cardNumber = document.querySelector('input[name="card_number"]');
        const expiryDate = document.querySelector('input[name="expiry_date"]');
        const cvv = document.querySelector('input[name="cvv"]');
        
        if (cardNumber && !validateCardNumber(cardNumber.value)) {
            showFieldError(cardNumber, 'Please enter a valid card number');
            return false;
        }
        
        if (expiryDate && !validateExpiryDate(expiryDate.value)) {
            showFieldError(expiryDate, 'Please enter a valid expiry date');
            return false;
        }
        
        if (cvv && !validateCVV(cvv.value)) {
            showFieldError(cvv, 'Please enter a valid CVV');
            return false;
        }
    }
    
    return true;
}

// Payment Methods
function initializePaymentMethods() {
    const paymentMethods = document.querySelectorAll('.payment-method');
    const paymentRadios = document.querySelectorAll('input[name="payment_method"]');
    
    paymentMethods.forEach(method => {
        method.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
                updatePaymentMethodSelection();
                togglePaymentFields(radio.value);
            }
        });
    });
    
    paymentRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            updatePaymentMethodSelection();
            togglePaymentFields(this.value);
        });
    });
}

function updatePaymentMethodSelection() {
    const paymentMethods = document.querySelectorAll('.payment-method');
    
    paymentMethods.forEach(method => {
        const radio = method.querySelector('input[type="radio"]');
        if (radio && radio.checked) {
            method.classList.add('selected');
        } else {
            method.classList.remove('selected');
        }
    });
}

function togglePaymentFields(paymentMethod) {
    const creditCardFields = document.querySelector('.credit-card-fields');
    
    if (creditCardFields) {
        if (paymentMethod === 'credit_card') {
            creditCardFields.style.display = 'block';
            creditCardFields.style.animation = 'fadeInUp 0.3s ease-out';
        } else {
            creditCardFields.style.display = 'none';
        }
    }
}

// Form Auto-save
function initializeFormAutoSave() {
    const formInputs = document.querySelectorAll('.checkout-form input, .checkout-form select, .checkout-form textarea');
    
    formInputs.forEach(input => {
        // Load saved value
        const savedValue = localStorage.getItem(`checkout_${input.name}`);
        if (savedValue && input.type !== 'password') {
            input.value = savedValue;
        }
        
        // Save on change
        input.addEventListener('input', function() {
            if (this.type !== 'password') {
                localStorage.setItem(`checkout_${this.name}`, this.value);
            }
        });
    });
}

// Validation Helpers
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validateCardNumber(cardNumber) {
    const cleaned = cardNumber.replace(/\s/g, '');
    return /^\d{13,19}$/.test(cleaned);
}

function validateExpiryDate(expiryDate) {
    const regex = /^(0[1-9]|1[0-2])\/\d{2}$/;
    if (!regex.test(expiryDate)) return false;
    
    const [month, year] = expiryDate.split('/');
    const expiry = new Date(2000 + parseInt(year), parseInt(month) - 1);
    const now = new Date();
    
    return expiry > now;
}

function validateCVV(cvv) {
    return /^\d{3,4}$/.test(cvv);
}

// Field Error Handling
function showFieldError(field, message) {
    const formGroup = field.closest('.form-group');
    if (!formGroup) return;
    
    formGroup.classList.add('error');
    
    // Remove existing error
    const existingError = formGroup.querySelector('.form-message.error');
    if (existingError) {
        existingError.remove();
    }
    
    // Add new error
    const errorElement = document.createElement('div');
    errorElement.className = 'form-message error';
    errorElement.textContent = message;
    
    field.parentNode.appendChild(errorElement);
}

function clearFieldError(field) {
    const formGroup = field.closest('.form-group');
    if (!formGroup) return;
    
    formGroup.classList.remove('error');
    
    const errorMessage = formGroup.querySelector('.form-message.error');
    if (errorMessage) {
        errorMessage.remove();
    }
}

// Order Submission
function submitOrder() {
    if (!validateCurrentStep()) {
        return false;
    }
    
    const form = document.querySelector('.checkout-form form');
    const submitBtn = document.querySelector('.btn-submit');
    
    // Add loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="loading-dots"><span></span><span></span><span></span></span> Processing Order...';
    
    // Add loading overlay
    document.querySelector('.checkout-container').classList.add('checkout-loading');
    
    // Submit form
    form.submit();
    
    // Clear saved form data
    clearFormAutoSave();
}

function clearFormAutoSave() {
    const formInputs = document.querySelectorAll('.checkout-form input, .checkout-form select, .checkout-form textarea');
    
    formInputs.forEach(input => {
        localStorage.removeItem(`checkout_${input.name}`);
    });
}

// Credit Card Formatting
function initializeCreditCardFormatting() {
    const cardNumberInput = document.querySelector('input[name="card_number"]');
    const expiryInput = document.querySelector('input[name="expiry_date"]');
    
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function() {
            let value = this.value.replace(/\s/g, '');
            let formattedValue = value.replace(/(.{4})/g, '$1 ').trim();
            this.value = formattedValue;
        });
    }
    
    if (expiryInput) {
        expiryInput.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            this.value = value;
        });
    }
}

// Initialize credit card formatting when payment method is selected
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeCreditCardFormatting, 100);
});

// Export functions for global use
window.nextStep = nextStep;
window.prevStep = prevStep;
window.submitOrder = submitOrder;
